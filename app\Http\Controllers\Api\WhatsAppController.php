<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\WhatsApp\WhatsAppProfile;
use App\Models\WhatsApp\WhatsAppMessage;
use App\Models\WhatsApp\WhatsAppAttachment;
use App\Events\WhatsAppAuthEvent;
use App\Events\WhatsAppMessageAck;
use App\Events\WhatsAppMessageReceived;
use App\Events\WhatsAppMessaging;
use App\Models\WhatsApp\WhatsAppConnectedUser;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WhatsAppController extends Controller
{

    /**
     * Handle WhatsApp authentication status updates
     */
    public function handleAuth(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|string',
            'status' => 'required|in:ready,authenticating,authenticated,connecting',
            'body' => 'nullable'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $userId = $request->user_id;
            $status = $request->status;
            $body = $request->body;

            // Broadcast the auth event via Reverb
            broadcast(new WhatsAppAuthEvent($userId, $status, $body));
            // Handle different status types
            switch ($status) {
                case 'ready':

                    break;

                case 'authenticating':

                    break;

                case 'authenticated':
                    // Successfully connected - body contains device info
                    // will be saved at the whatsapp settings component
                    break;

                case 'connecting':

                    break;
            }

            return response()->json([
                'success' => true,
                'message' => 'Auth status received and broadcasted',
                'status' => $status
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error processing auth status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process incoming WhatsApp message - DOES EVERYTHING IN ONE CALL
     */
    public function processIncomingMessage(Request $request): JsonResponse
    {
        return DB::transaction(function () use ($request) {
            // 1. Fast profile lookup/create (private chats only)
            $profileData = [
                'phone_number' => $request->contact_number ?? 'unknown',
                'name' => $request->contact_name ?? 'Unknown Contact',
            ];

            // Add profile picture if provided
            if ($request->contact_profile_picture) {
                $profileData['profile_picture'] = $request->contact_profile_picture;
            }

            $profile = WhatsAppProfile::firstOrCreate(
                ['whatsapp_id' => $request->whatsapp_contact_id],
                $profileData
            );

            // Update existing profile with new information if provided
            if ($profile->wasRecentlyCreated === false) {
                $updateData = [];

                // Update name if we have a better one
                if ($request->contact_name && $request->contact_name !== 'Unknown Contact' && $profile->name === 'Unknown Contact') {
                    $updateData['name'] = $request->contact_name;
                }

                // Update phone number if we have one and current is unknown
                if ($request->contact_number && $request->contact_number !== 'unknown' && $profile->phone_number === 'unknown') {
                    $updateData['phone_number'] = $request->contact_number;
                }

                // Update profile picture if provided
                if ($request->contact_profile_picture) {
                    $updateData['profile_picture'] = $request->contact_profile_picture;
                }

                if (!empty($updateData)) {
                    $profile->update($updateData);
                    $profile->refresh(); // Refresh to get updated data
                }
            }

            // 2. Create Message first
            $message = WhatsAppMessage::create([
                'message_id' => $request->whatsapp_message_id,
                'profile_id' => $profile->id,
                'type' => $this->mapMessageType($request->message_type ?? 'chat'),
                'content' => $request->message_body,
                'is_outgoing' => $request->is_outgoing ?? false,
                'is_read' => false,
                'sent_at' => date('Y-m-d H:i:s', $request->timestamp ?? time()),
            ]);

            // 3. Handle media if present - store as attachment
            if ($request->has('media_data') && !empty($request->media_data)) {
                Log::info("Processing media data for message: " . $request->whatsapp_message_id);
                try {
                    // Decode base64 media data
                    $mediaData = base64_decode($request->media_data);

                    if ($mediaData) {
                        // Generate unique filename with proper extension
                        $timestamp = now()->timestamp;
                        $extension = $this->getExtensionFromMimeType($request->media_mimetype ?? 'application/octet-stream');
                        $filename = $request->media_filename ?? "media_{$timestamp}";

                        // Ensure filename has proper extension
                        if (!pathinfo($filename, PATHINFO_EXTENSION)) {
                            $filename .= ".{$extension}";
                        }

                        // Store file in public storage so it's accessible via URL
                        $filePath = "whatsapp/attachments/{$filename}";

                        Log::info("Attempting to save file: {$filePath}, size: " . strlen($mediaData) . " bytes");

                        $saved = Storage::disk('public')->put($filePath, $mediaData);

                        if ($saved) {
                            Log::info("File saved successfully to: " . Storage::disk('public')->path($filePath));

                            // Verify file exists
                            if (Storage::disk('public')->exists($filePath)) {
                                Log::info("File verified to exist at: {$filePath}");
                            } else {
                                Log::error("File does not exist after saving: {$filePath}");
                            }
                        } else {
                            Log::error("Failed to save file: {$filePath}");
                        }

                        // Create attachment record
                        $attachment = WhatsAppAttachment::create([
                            'message_id' => $message->id,
                            'filename' => $filename,
                            'mime_type' => $request->media_mimetype ?? 'application/octet-stream',
                            'file_size' => strlen($mediaData),
                        ]);

                        Log::info("Media attachment record created: {$filename}, attachment ID: " . $attachment->id);
                    } else {
                        Log::warning("Failed to decode media data for message: " . $request->whatsapp_message_id);
                    }
                } catch (\Exception $e) {
                    Log::error("Error processing media for message {$request->whatsapp_message_id}: " . $e->getMessage());
                }
            } else {
                if ($request->has_media) {
                    Log::warning("Message has media flag but no media_data received for message: " . $request->whatsapp_message_id);
                }
            }

            // 4. Broadcast the event via Reverb
            // Determine user ID - use request user_id or default to 1 for now
            $userId = $request->user_id ?? 1; // TODO: Determine proper user ID logic

            broadcast(new WhatsAppMessaging(
                $userId,
                $profile->id
            ));

            // 5. Return processed data
            return response()->json([
                'success' => true,
                'data' => [
                    'message' => $message,
                    'profile' => $profile,
                    'chat' => [
                        'id' => $request->chat_id,
                        'name' => $request->chat_name,
                        'isGroup' => $request->is_group,
                    ]
                ]
            ]);
        });
    }

    private function mapMessageType($whatsappType): string
    {
        $typeMap = [
            'chat' => 'text',
            'image' => 'image',
            'video' => 'video',
            'audio' => 'audio',
            'ptt' => 'audio',
            'document' => 'document',
            'location' => 'location',
            'vcard' => 'contact',
        ];

        return $typeMap[$whatsappType] ?? 'text';
    }

    /**
     * Get file extension from MIME type
     */
    private function getExtensionFromMimeType(string $mimeType): string
    {
        $mimeToExtension = [
            'image/jpeg' => 'jpg',
            'image/jpg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp',
            'video/mp4' => 'mp4',
            'video/avi' => 'avi',
            'video/mov' => 'mov',
            'video/quicktime' => 'mov',
            'audio/mpeg' => 'mp3',
            'audio/mp3' => 'mp3',
            'audio/wav' => 'wav',
            'audio/ogg' => 'ogg',
            'audio/aac' => 'aac',
            'application/pdf' => 'pdf',
            'application/msword' => 'doc',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'docx',
            'text/plain' => 'txt',
        ];

        return $mimeToExtension[$mimeType] ?? 'bin';
    }


    /**
     * Update message acknowledgment
     */
    public function updateMessageAck(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'ack' => 'required|string|min:0',
            'message_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $message = WhatsAppMessage::where('message_id', $request->message_id)->first();

            if (!$message) {
                return response()->json([
                    'success' => false,
                    'message' => 'Message not found'
                ], 404);
            }

            if ($request->ack === 'delivered') {
                $message->update(['is_delivered' => true]);
            } elseif ($request->ack === 'read') {
                $message->update(['is_read' => true]);
            }

            // Broadcast the event via Reverb
            broadcast(new WhatsAppMessaging(
                $request->user_id,
                $message->profile->id,
                'message_ack'
            ));

            return response()->json([
                'success' => true,
                'data' => $message
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating message ACK: ' . $e->getMessage()
            ], 500);
        }
    }
}
