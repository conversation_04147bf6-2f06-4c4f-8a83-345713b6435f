<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'whatsapp';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('messages', function (Blueprint $table) {
            $table->id();
            $table->string('message_id')->unique(); // WhatsApp message ID
            $table->foreignId('profile_id')->constrained()->onDelete('cascade'); // Foreign key to profiles
            $table->enum('type', ['text', 'image', 'video', 'audio', 'document', 'location', 'contact']);
            $table->text('content')->nullable(); // Message content
            $table->boolean('is_outgoing')->default(false); // true if sent by us
            $table->boolean('is_read')->default(false);
            $table->boolean('is_deleted')->default(false);
            $table->boolean('is_delivered')->default(false);
            $table->timestamp('sent_at');
            $table->timestamps();
            
            $table->index('profile_id');
            $table->index('is_outgoing');
            $table->index('is_read');
            $table->index('sent_at');
            $table->index(['profile_id', 'sent_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('messages');
    }
};
