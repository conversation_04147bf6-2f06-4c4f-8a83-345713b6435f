/**
 * WhatsApp Messaging Handler
 * Handles sending messages through WhatsApp API
 */

let isSending = false;
let messageQueue = [];
let currentUserId = null;

// Get user ID from <PERSON><PERSON> (you can customize this based on your auth system)
function getUserId() {
    // Try to get user ID from meta tag
    const metaTag = document.querySelector('meta[name="user-id"]');
    if (metaTag) {
        return metaTag.getAttribute('content');
    }

    // Fallback: try to get from window.Laravel if available
    if (window.Laravel && window.Laravel.user && window.Laravel.user.id) {
        return window.Laravel.user.id.toString();
    }

    // Fallback: generate a session-based ID
    let sessionId = sessionStorage.getItem('whatsapp_session_id');
    if (!sessionId) {
        sessionId = 'session_' + Math.random().toString(36).substring(2, 11);
        sessionStorage.setItem('whatsapp_session_id', sessionId);
    }
    return sessionId;
}

// Get auth token (customize based on your auth system)
function getAuthToken() {
    const metaTag = document.querySelector('meta[name="csrf-token"]');
    return metaTag ? metaTag.getAttribute('content') : null;
}

/**
 * Send a WhatsApp message
 * @param {Object} messageData - Message data containing recipient and message
 * @param {Object} callbacks - Callback functions for different states
 */
export function sendWhatsAppMessage(messageData, callbacks = {}) {
    const {
        onSending,
        onSuccess,
        onError,
        onQueueAdded
    } = callbacks;

    // Validate message data
    if (!messageData.recipient || !messageData.message) {
        const error = 'Recipient and message are required';
        console.error('❌ WhatsApp Message Error:', error);
        if (onError) onError(error);
        return Promise.reject(new Error(error));
    }

    // Add to queue if currently sending
    if (isSending) {
        messageQueue.push({ messageData, callbacks });
        console.log('📝 Message added to queue');
        if (onQueueAdded) onQueueAdded();
        return Promise.resolve({ queued: true });
    }

    return sendMessage(messageData, callbacks);
}

/**
 * Internal function to send message
 */
async function sendMessage(messageData, callbacks = {}) {
    const {
        onSending,
        onSuccess,
        onError
    } = callbacks;

    isSending = true;
    currentUserId = getUserId();

    try {
        console.log('📤 Sending WhatsApp message...');
        if (onSending) onSending();

        const response = await fetch('/api/whatsapp/send-message', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': getAuthToken(),
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                user_id: currentUserId,
                recipient: messageData.recipient,
                message: messageData.message,
                type: messageData.type || 'text'
            })
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.message || `HTTP error! status: ${response.status}`);
        }

        console.log('✅ Message sent successfully:', data);
        if (onSuccess) onSuccess(data);

        return data;

    } catch (error) {
        console.error('❌ Failed to send message:', error);
        if (onError) onError(error.message);
        throw error;
    } finally {
        isSending = false;
        processQueue();
    }
}

/**
 * Process queued messages
 */
async function processQueue() {
    if (messageQueue.length === 0 || isSending) {
        return;
    }

    const { messageData, callbacks } = messageQueue.shift();
    try {
        await sendMessage(messageData, callbacks);
    } catch (error) {
        console.error('❌ Failed to process queued message:', error);
    }
}

/**
 * Send bulk messages
 * @param {Array} messages - Array of message objects
 * @param {Object} callbacks - Callback functions
 */
export function sendBulkMessages(messages, callbacks = {}) {
    const {
        onProgress,
        onComplete,
        onError
    } = callbacks;

    let completed = 0;
    let failed = 0;
    const results = [];

    const processMessage = async (messageData, index) => {
        try {
            const result = await sendWhatsAppMessage(messageData, {
                onSuccess: (data) => {
                    completed++;
                    results[index] = { success: true, data };
                    if (onProgress) onProgress({ completed, failed, total: messages.length });
                },
                onError: (error) => {
                    failed++;
                    results[index] = { success: false, error };
                    if (onProgress) onProgress({ completed, failed, total: messages.length });
                }
            });
        } catch (error) {
            failed++;
            results[index] = { success: false, error: error.message };
            if (onProgress) onProgress({ completed, failed, total: messages.length });
        }

        // Check if all messages are processed
        if (completed + failed === messages.length) {
            if (onComplete) onComplete(results);
        }
    };

    // Process all messages
    messages.forEach((messageData, index) => {
        processMessage(messageData, index);
    });
}

/**
 * Get message queue status
 */
export function getQueueStatus() {
    return {
        isSending,
        queueLength: messageQueue.length,
        currentUserId
    };
}

/**
 * Clear message queue
 */
export function clearQueue() {
    messageQueue = [];
    console.log('🗑️ Message queue cleared');
}

/**
 * Check if messaging is available
 */
export async function checkMessagingStatus() {
    try {
        const response = await fetch('/api/whatsapp/check-connection', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': getAuthToken(),
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                user_id: getUserId()
            })
        });

        const data = await response.json();
        return data.status === 'authenticated';
    } catch (error) {
        console.error('❌ Failed to check messaging status:', error);
        return false;
    }
}

/**
 * Format phone number for WhatsApp
 * @param {string} phoneNumber - Phone number to format
 */
export function formatPhoneNumber(phoneNumber) {
    // Remove all non-digit characters
    let cleaned = phoneNumber.replace(/\D/g, '');
    
    // Add country code if not present (assuming default country code)
    if (!cleaned.startsWith('1') && cleaned.length === 10) {
        cleaned = '1' + cleaned;
    }
    
    return cleaned + '@c.us';
}

/**
 * Validate phone number format
 * @param {string} phoneNumber - Phone number to validate
 */
export function validatePhoneNumber(phoneNumber) {
    const cleaned = phoneNumber.replace(/\D/g, '');
    return cleaned.length >= 10 && cleaned.length <= 15;
}

// Export utilities
export const WhatsAppMessaging = {
    sendMessage: sendWhatsAppMessage,
    sendBulkMessages,
    getQueueStatus,
    clearQueue,
    checkMessagingStatus,
    formatPhoneNumber,
    validatePhoneNumber
};
