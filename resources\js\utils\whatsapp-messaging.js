/**
 * WhatsApp Messaging Handler
 * Handles sending messages through WhatsApp API
 * Uses message ACK events for status updates instead of recursive handling
 */

let isSending = false;
let messageQueue = [];
let currentUserId = null;
let messageCallbacks = new Map(); // Store callbacks for message ACK handling
let reverbChannel = null;
let isListening = false;

// Get user ID from Lara<PERSON> (you can customize this based on your auth system)
function getUserId() {
    // Try to get user ID from meta tag
    const metaTag = document.querySelector('meta[name="user-id"]');
    if (metaTag) {
        return metaTag.getAttribute('content');
    }

    // Fallback: try to get from window.Laravel if available
    if (window.Laravel && window.Laravel.user && window.Laravel.user.id) {
        return window.Laravel.user.id.toString();
    }

    // Fallback: generate a session-based ID
    let sessionId = sessionStorage.getItem('whatsapp_session_id');
    if (!sessionId) {
        sessionId = 'session_' + Math.random().toString(36).substring(2, 11);
        sessionStorage.setItem('whatsapp_session_id', sessionId);
    }
    return sessionId;
}

// Get auth token (customize based on your auth system)
function getAuthToken() {
    const metaTag = document.querySelector('meta[name="csrf-token"]');
    return metaTag ? metaTag.getAttribute('content') : null;
}

/**
 * Initialize messaging system with Reverb connection
 */
export function initializeMessaging() {
    setupReverbConnection();
}

/**
 * Cleanup messaging system
 */
export function cleanupMessaging() {
    cleanupReverbConnection();
    clearQueue();
    messageCallbacks.clear();
}

/**
 * Send a WhatsApp message
 * @param {Object} messageData - Message data containing recipient and message
 * @param {Object} options - Optional configuration
 */
export function sendWhatsAppMessage(messageData, options = {}) {
    // Ensure Reverb connection is setup
    if (!isListening) {
        setupReverbConnection();
    }

    // Validate message data
    if (!messageData.recipient || !messageData.message) {
        const error = 'Recipient and message are required';
        console.error('❌ WhatsApp Message Error:', error);
        return Promise.reject(new Error(error));
    }

    // Add to queue if currently sending
    if (isSending) {
        messageQueue.push({ messageData, options });
        console.log('📝 Message added to queue');
        return Promise.resolve({ queued: true });
    }

    return sendMessage(messageData, options);
}

/**
 * Internal function to send message
 * Message status updates will be handled by ACK events, not here
 */
async function sendMessage(messageData, options = {}) {
    isSending = true;
    currentUserId = getUserId();

    try {
        console.log('📤 Sending WhatsApp message...');

        const response = await fetch('/api/whatsapp/send-message', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': getAuthToken(),
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                user_id: currentUserId,
                recipient: messageData.recipient,
                message: messageData.message,
                type: messageData.type || 'text'
            })
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.message || `HTTP error! status: ${response.status}`);
        }

        console.log('✅ Message sent successfully. Status updates will come via ACK events.');

        // Store message ID for ACK tracking if needed
        if (data.messageId && options.trackAck) {
            messageCallbacks.set(data.messageId, options.onAck);
        }

        return data;

    } catch (error) {
        console.error('❌ Failed to send message:', error);
        throw error;
    } finally {
        isSending = false;
        processQueue();
    }
}

/**
 * Process queued messages
 */
async function processQueue() {
    if (messageQueue.length === 0 || isSending) {
        return;
    }

    const { messageData, options } = messageQueue.shift();
    try {
        await sendMessage(messageData, options);
    } catch (error) {
        console.error('❌ Failed to process queued message:', error);
    }
}

/**
 * Send bulk messages
 * @param {Array} messages - Array of message objects
 * @param {Object} callbacks - Callback functions
 */
export function sendBulkMessages(messages, callbacks = {}) {
    const { onProgress, onComplete } = callbacks;

    let completed = 0;
    let failed = 0;
    const results = [];

    const processMessage = async (messageData, index) => {
        try {
            await sendWhatsAppMessage(messageData);
            completed++;
            results[index] = { success: true, messageData };
            if (onProgress) onProgress({ completed, failed, total: messages.length });
        } catch (error) {
            failed++;
            results[index] = { success: false, error: error.message };
            if (onProgress) onProgress({ completed, failed, total: messages.length });
        }

        // Check if all messages are processed
        if (completed + failed === messages.length) {
            if (onComplete) onComplete(results);
        }
    };

    // Process all messages
    messages.forEach((messageData, index) => {
        processMessage(messageData, index);
    });
}

/**
 * Get message queue status
 */
export function getQueueStatus() {
    return {
        isSending,
        queueLength: messageQueue.length,
        currentUserId
    };
}

/**
 * Clear message queue
 */
export function clearQueue() {
    messageQueue = [];
    console.log('🗑️ Message queue cleared');
}

/**
 * Check if messaging is available
 */
export async function checkMessagingStatus() {
    try {
        const response = await fetch('/api/whatsapp/check-connection', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': getAuthToken(),
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                user_id: getUserId()
            })
        });

        const data = await response.json();
        return data.status === 'authenticated';
    } catch (error) {
        console.error('❌ Failed to check messaging status:', error);
        return false;
    }
}

/**
 * Format phone number for WhatsApp
 * @param {string} phoneNumber - Phone number to format
 */
export function formatPhoneNumber(phoneNumber) {
    // Remove all non-digit characters
    let cleaned = phoneNumber.replace(/\D/g, '');

    // Add country code if not present (assuming default country code)
    if (!cleaned.startsWith('1') && cleaned.length === 10) {
        cleaned = '1' + cleaned;
    }

    return cleaned + '@c.us';
}

/**
 * Validate phone number format
 * @param {string} phoneNumber - Phone number to validate
 */
export function validatePhoneNumber(phoneNumber) {
    const cleaned = phoneNumber.replace(/\D/g, '');
    return cleaned.length >= 10 && cleaned.length <= 15;
}

/**
 * Setup Reverb connection for message ACK events
 */
function setupReverbConnection() {
    if (isListening) {
        console.log('⚠️ Already listening, skipping setup');
        return;
    }

    if (!window.Echo) {
        console.error('❌ Echo not available, retrying in 1 second...');
        setTimeout(setupReverbConnection, 1000);
        return;
    }

    currentUserId = getUserId();
    const channelName = `whatsapp.${currentUserId}`;

    try {
        console.log(`🔗 Setting up Reverb connection for channel: ${channelName}`);

        reverbChannel = window.Echo.private(channelName);

        // Add connection event listeners for debugging
        reverbChannel.subscribed(() => {
            console.log(`✅ Successfully subscribed to channel: ${channelName}`);
        });

        reverbChannel.error((error) => {
            console.error(`❌ Channel subscription error for ${channelName}:`, error);
        });

        // Listen for the Messaging event
        reverbChannel.listen('Messaging', (data) => {
            console.log('📨 Reverb message status event received:', data);
            // Handle different types of events
            if (data.type === 'message_ack') {
                handleMessageAck(data.profile_id);
            } else if (data.type === 'message_received' || data.type === 'message_recived') {
                // Handle both correct spelling and the typo in the event
                handleMessageReceived(data.profile_id);
            }
        });

        // Listen for any event (debugging) - try different event name formats
        reverbChannel.listen('.Messaging', (data) => {
            console.log('📨 Reverb event with dot prefix received:', data);
            // Handle the event here too since it's working
            if (data.type === 'message_ack') {
                handleMessageAck(data.profile_id);
            } else if (data.type === 'message_received' || data.type === 'message_recived') {
                handleMessageReceived(data.profile_id);
            }
        });

        // Listen for ALL events on this channel (catch-all debugging)
        reverbChannel.listenForWhisper('*', (data) => {
            console.log('👂 Whisper event received:', data);
        });

        // Try to listen to any event with a wildcard
        if (reverbChannel.bind) {
            reverbChannel.bind('*', (data) => {
                console.log('🌟 ANY event received on channel:', data);
            });
        }

        // Listen for all events on this channel (debugging)
        reverbChannel.notification((notification) => {
            console.log('🔔 Channel notification received:', notification);
        });

        isListening = true;
        console.log(`✅ Listening for message events on channel: ${channelName}`);

    } catch (error) {
        console.error('❌ Failed to setup Reverb connection for messaging:', error);
    }
}

/**
 * Cleanup Reverb connection
 */
function cleanupReverbConnection() {
    if (reverbChannel) {
        try {
            reverbChannel.stopListening('Messaging');
            window.Echo.leave(`whatsapp.${currentUserId}`);
            reverbChannel = null;
            isListening = false;
            console.log('🧹 Messaging Reverb connection cleaned up');
        } catch (error) {
            console.error('❌ Error cleaning up messaging Reverb connection:', error);
        }
    }
}

/**
 * Handle message ACK events - updates message status globally
 * @param {string} profileId - Profile ID to update messages for
 */
function handleMessageAck(profileId) {
    console.log(`📨 Handling message ACK for profile: ${profileId}`);

    // Dispatch custom event that can be handled in the view
    window.dispatchEvent(new CustomEvent('whatsapp-message-ack', {
        detail: { profileId }
    }));
}

/**
 * Handle message received events - fetches new messages globally
 * @param {string} profileId - Profile ID to fetch messages for
 */
function handleMessageReceived(profileId) {
    console.log(`📨 Handling message received for profile: ${profileId}`);

    // Dispatch custom event that can be handled in the view
    window.dispatchEvent(new CustomEvent('whatsapp-message-received', {
        detail: { profileId }
    }));
}

// Export utilities
export const WhatsAppMessaging = {
    initialize: initializeMessaging,
    cleanup: cleanupMessaging,
    sendMessage: sendWhatsAppMessage,
    sendBulkMessages,
    getQueueStatus,
    clearQueue,
    checkMessagingStatus,
    formatPhoneNumber,
    validatePhoneNumber,
    handleMessageAck: handleMessageAck,
    handleMessageReceived: handleMessageReceived
};
