const http = require('http');
const https = require('https');
const config = require('./config');

class ApiService {
    constructor() {
        this.baseUrl = config.api.laravelUrl;
        this.timeout = config.api.timeout;
        this.enabled = !!this.baseUrl; // Only enable if Laravel URL is provided
    }

    // HTTP request helper with connection pooling
    async makeRequest(method, endpoint, data = null) {
        return new Promise((resolve, reject) => {
            const url = `${this.baseUrl}${endpoint}`;
            const urlObj = new URL(url);
            const isHttps = urlObj.protocol === 'https:';
            const httpModule = isHttps ? https : http;

            const options = {
                hostname: urlObj.hostname,
                port: urlObj.port || (isHttps ? 443 : 80),
                path: urlObj.pathname + urlObj.search,
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                timeout: this.timeout
            };

            if (data) {
                const jsonData = JSON.stringify(data);
                options.headers['Content-Length'] = Buffer.byteLength(jsonData);
            }

            const req = httpModule.request(options, (res) => {
                let responseData = '';

                res.on('data', (chunk) => {
                    responseData += chunk;
                });

                res.on('end', () => {
                    try {
                        const parsedData = JSON.parse(responseData);
                        if (res.statusCode >= 200 && res.statusCode < 300) {
                            resolve(parsedData);
                        } else {
                            reject(new Error(`HTTP ${res.statusCode}: ${parsedData.message || responseData}`));
                        }
                    } catch (error) {
                        reject(new Error(`Invalid JSON response: ${responseData}`));
                    }
                });
            });

            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('Request timeout'));
            });

            if (data) {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    // Process incoming messages
    async processIncomingMessage(rawMessageData) {
        if (!this.enabled) {
            console.log('📝 API service disabled - skipping message processing');
            return { message: { id: rawMessageData.whatsapp_message_id }, profile: {}, chat: {} };
        }

        try {
            const response = await this.makeRequest('POST', '/whatsapp/process-message', rawMessageData);
            return response.data;
        } catch (error) {
            console.error('❌ Error processing message:', error.message);
            return { message: { id: rawMessageData.whatsapp_message_id }, profile: {}, chat: {} }; // Fallback
        }
    }

    async updateMessageAck(ackData) {
        if (!this.enabled) {
            console.log('📝 API service disabled - skipping message ack update');
            return;
        }

        try {
            const response = await this.makeRequest('POST', '/whatsapp/update-message-ack', ackData);
            return response.data;
        } catch (error) {
            console.error('❌ Error updating message ack:', error.message);
        }
    }

    // Single auth endpoint for all authentication states
    async notifyAuth(userId, status, body) {
        if (!this.enabled) {
            console.log('📝 API service disabled - skipping auth notification');
            return;
        }

        try {
            await this.makeRequest('POST', '/whatsapp/auth', {
                user_id: userId,
                status: status,
                body: body
            });
        } catch (error) {
            console.error('❌ Error notifying auth:', error.message);
        }
    }

    // Send message via API request
    async sendMessage(userId, chatId, message, type = 'text', media = null) {
        if (!this.enabled) {
            console.log('📝 API service disabled - cannot send message');
            return { success: false, error: 'API service disabled' };
        }

        try {
            const response = await this.makeRequest('POST', '/whatsapp/send-message', {
                user_id: userId,
                chat_id: chatId,
                message: message,
                type: type,
                media: media
            });
            return response;
        } catch (error) {
            console.error('❌ Error sending message via API:', error.message);
            return { success: false, error: error.message };
        }
    }
}

module.exports = ApiService;
