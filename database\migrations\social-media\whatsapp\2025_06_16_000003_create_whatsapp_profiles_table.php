<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'whatsapp';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('connected_user_id')->constrained()->onDelete('cascade');
            $table->string('whatsapp_id')->unique(); // WhatsApp contact ID (e.g., <EMAIL>)
            $table->string('phone_number');
            $table->string('name');
            $table->string('profile_picture')->nullable();
            $table->timestamps();

            // Only essential indexes for private chats
            $table->index('connected_user_id');
            $table->index('whatsapp_id');
            $table->index('phone_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('profiles');
    }
};
