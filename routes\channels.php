<?php

use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Log;


Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('whatsapp.auth.{userId}', function ($user, $userId) {
    Log::info('Broadcasting on whatsapp.auth channel for user: ' . $userId);
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('whatsapp.{userId}', function ($user, $userId) {
    Log::info('Broadcasting on whatsapp channel for user: ' . $userId);
    return (int) $user->id === (int) $userId;
});
