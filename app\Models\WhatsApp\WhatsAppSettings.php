<?php

namespace App\Models\WhatsApp;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Traits\CreatesDatabaseFile;
use App\Models\User;

class WhatsAppSettings extends Model
{
    use HasFactory, CreatesDatabaseFile;

    protected $connection = 'whatsapp';
    protected $table = 'wa_settings';

    protected $fillable = [
        'user_id',
        'auto_reply',
        'welcome_message',
        'connected_ai_model',
    ];

    protected $casts = [
        'auto_reply' => 'boolean',
    ];

    /**
     * Get the settings for a specific user with caching
     */
    public static function forUser($userId)
    {
        return static::where('user_id', $userId)->first();
    }

    /**
     * Get the settings for a specific user with only specific fields for performance
     */
    public static function forUserSelect($userId, array $fields = ['*'])
    {
        return static::select($fields)->where('user_id', $userId)->first();
    }

    /**
     * Update or create settings with optimized query
     */
    public static function updateOrCreateForUser($userId, array $data)
    {
        return static::updateOrCreate(
            ['user_id' => $userId],
            $data
        );
    }

    /**
     * Get the Laravel user that owns these settings
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get all connected users for these settings
     */
    public function connectedUsers(): HasMany
    {
        return $this->hasMany(WhatsAppConnectedUser::class, 'wa_settings_id');
    }

    /**
     * Get the first connected user (for convenience)
     */
    public function connectedUser()
    {
        return $this->hasOne(WhatsAppConnectedUser::class, 'wa_settings_id');
    }

    /**
     * Check if user is currently connected
     */
    public function isConnected()
    {
        return $this->connectedUsers()->exists();
    }

    /**
     * Get settings with connection status for a user
     */
    public static function forUserWithConnection($userId)
    {
        return static::with('connectedUsers')->where('user_id', $userId)->first();
    }
}
