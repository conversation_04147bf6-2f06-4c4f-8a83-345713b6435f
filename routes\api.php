<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\WhatsAppController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// WhatsApp API Routes - PRIVATE CHATS ONLY
Route::prefix('whatsapp')->group(function () {
    // MAIN ENDPOINT - processes everything in one call
    Route::post('/process-message', [WhatsAppController::class, 'processIncomingMessage']);
    Route::post('/update-message-ack', [WhatsAppController::class, 'updateMessageAck']);

    // WhatsApp Authentication API Routes
    Route::post('/auth', [WhatsAppController::class, 'handleAuth']);
});
