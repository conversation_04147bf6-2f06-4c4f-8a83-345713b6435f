<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'whatsapp';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('connected_users', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('session_id');
            $table->string('phone_number')->nullable();
            $table->string('profile_name')->nullable();
            $table->string('profile_picture_url')->nullable();
            $table->string('device_model')->nullable();
            $table->string('platform')->default('web');
            $table->timestamps();

            $table->index('user_id');
            $table->index('session_id');
            $table->index('phone_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('connected_users');
    }
};
