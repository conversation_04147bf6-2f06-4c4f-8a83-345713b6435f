<div class="contents">
    <!-- Sidebar -->
    <div class="sidebar print:hidden">
        <!-- Main Sidebar -->
        <x-app-partials.main-sidebar></x-app-partials.main-sidebar>
        <!-- Sidebar Panel -->

        @include('livewire.social-bots.whatsapp.sidebar-panel')

        <!-- Minimized Sidebar Panel -->

        @include('livewire.social-bots.whatsapp.minimized-sidebar-panel')

    </div>

    <!-- Right Sidebar -->
    <x-app-partials.right-sidebar></x-app-partials.right-sidebar>

    <!-- Main Content Wrapper -->
    <main x-data="{ isShowChatInfo: !$store.breakpoints.mdAndDown }" x-effect="$store.breakpoints.mdAndDown === true && (isShowChatInfo = false)"
        class="main-content h-100vh chat-app mt-0 flex w-full flex-col" :class="isShowChatInfo && 'lg:mr-80'">

        @include('livewire.social-bots.whatsapp.chat-header')
        @include('livewire.social-bots.whatsapp.message-area')


        <div
            class="chat-footer relative flex h-12 w-full shrink-0 items-center justify-between border-t border-slate-150 bg-white px-[calc(var(--margin-x)-.25rem)] transition-[padding,width] duration-[.25s] dark:border-navy-600 dark:bg-navy-800">
            @if ($selectedConversation)
                <form wire:submit.prevent="sendMessage" class="flex w-full items-center space-x-2">
                    <div class="-ml-1.5 flex flex-1 space-x-2">
                        <button type="button"
                            class="btn size-9 shrink-0 rounded-full p-0 text-slate-500 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:text-navy-200 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                            <svg xmlns="http://www.w3.org/2000/svg" class="size-5.5" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor" stroke-width="1.5">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                            </svg>
                        </button>

                        <input type="text" wire:model="newMessage" wire:keydown.enter="sendMessage"
                            class="form-input h-9 w-full bg-transparent placeholder:text-slate-400/70"
                            placeholder="Write the message" wire:loading.attr="disabled" wire:target="sendMessage" />
                    </div>

                    <div class="-mr-1.5 flex">
                        <button type="button"
                            class="btn size-9 shrink-0 rounded-full p-0 text-slate-500 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:text-navy-200 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                            <svg xmlns="http://www.w3.org/2000/svg" class="size-5.5" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor" stroke-width="1.5">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </button>
                        <button type="submit" wire:loading.attr="disabled" wire:target="sendMessage"
                            class="btn size-9 shrink-0 rounded-full p-0 text-primary hover:bg-primary/20 focus:bg-primary/20 active:bg-primary/25 dark:text-accent-light dark:hover:bg-accent-light/20 dark:focus:bg-accent-light/20 dark:active:bg-accent-light/25 disabled:opacity-50 disabled:cursor-not-allowed">

                            <!-- Loading spinner for sending -->
                            <div wire:loading wire:target="sendMessage"
                                class="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>

                            <!-- Send icon (hidden when loading) -->
                            <svg wire:loading.remove wire:target="sendMessage" xmlns="http://www.w3.org/2000/svg"
                                class="size-5.5" fill="none" viewBox="0 0 24 24" stroke="currentColor"
                                stroke-width="1.5">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="m9.813 5.146 9.027 3.99c4.05 1.79 4.05 4.718 0 6.508l-9.027 3.99c-6.074 2.686-8.553.485-5.515-4.876l.917-1.613c.232-.41.232-1.09 0-1.5l-.917-1.623C1.26 4.66 3.749 2.46 9.813 5.146ZM6.094 12.389h7.341" />
                            </svg>
                        </button>
                    </div>
                </form>
            @else
                <!-- No conversation selected -->
                <div class="flex w-full items-center justify-center">
                    <p class="text-sm text-slate-500 dark:text-navy-300">Select a conversation to start messaging</p>
                </div>
            @endif
        </div>

        <template x-teleport="#x-teleport-target">
            <div x-data="{
                get showDrawer() { return $data.isShowChatInfo; },
                set showDrawer(val) { $data.isShowChatInfo = val; },
            }" x-show="showDrawer" @keydown.window.escape="showDrawer = false">
                <div class="fixed inset-0 z-100 bg-slate-900/60 transition-opacity duration-200"
                    @click="showDrawer = false" x-show="showDrawer && $store.breakpoints.mdAndDown"
                    x-transition:enter="ease-out" x-transition:enter-start="opacity-0"
                    x-transition:enter-end="opacity-100" x-transition:leave="ease-in"
                    x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"></div>
                <div class="fixed right-0 top-0 z-101 h-full w-full sm:w-80">
                    <div class="flex h-full w-full flex-col border-l border-slate-150 bg-white transition-transform duration-200 dark:border-navy-600 dark:bg-navy-750"
                        x-show="showDrawer" x-transition:enter="ease-out transform-gpu"
                        x-transition:enter-start="translate-x-full" x-transition:enter-end="translate-x-0"
                        x-transition:leave="ease-in transform-gpu" x-transition:leave-start="translate-x-0"
                        x-transition:leave-end="translate-x-full">
                        <div class="flex h-[60px] items-center justify-between p-4">
                            <h3 class="text-base font-medium text-slate-700 dark:text-navy-100">
                                Chat Info
                            </h3>
                            <div class="-mr-1.5 flex space-x-1">
                                <button @click="$store.global.isRightSidebarExpanded = true"
                                    class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="size-5.5 text-slate-500 dark:text-navy-100" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                            d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                                    </svg>
                                </button>

                                <button @click="$store.global.isDarkModeEnabled = !$store.global.isDarkModeEnabled"
                                    class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:active:bg-navy-300/25">
                                    <svg x-show="$store.global.isDarkModeEnabled"
                                        x-transition:enter="transition-transform duration-200 ease-out absolute origin-top"
                                        x-transition:enter-start="scale-75" x-transition:enter-end="scale-100 static"
                                        class="size-6 text-amber-400" fill="currentColor" viewBox="0 0 24 24">
                                        <path
                                            d="M11.75 3.412a.818.818 0 01-.07.917 6.332 6.332 0 00-1.4 3.971c0 3.564 2.98 6.494 6.706 6.494a6.86 6.86 0 002.856-.617.818.818 0 011.1 1.047C19.593 18.614 16.218 21 12.283 21 7.18 21 3 16.973 3 11.956c0-4.563 3.46-8.31 7.925-8.948a.818.818 0 01.826.404z" />
                                    </svg>
                                    <svg xmlns="http://www.w3.org/2000/svg" x-show="!$store.global.isDarkModeEnabled"
                                        x-transition:enter="transition-transform duration-200 ease-out absolute origin-top"
                                        x-transition:enter-start="scale-75" x-transition:enter-end="scale-100 static"
                                        class="size-6 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd"
                                            d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </button>

                                <button @click="showDrawer=false"
                                    class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="size-5" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="mt-5 flex flex-col items-center">
                            @if ($selectedConversation)
                                <div class="avatar size-20">
                                    <img class="rounded-full" src="{{ $selectedConversation['profile_picture'] }}"
                                        alt="avatar" />
                                </div>
                                <h3 class="mt-2 text-lg font-medium text-slate-700 dark:text-navy-100">
                                    {{ $selectedConversation['name'] }}</h3>
                                <p class="text-sm text-slate-500 dark:text-navy-300">
                                    {{ $selectedConversation['phone_number'] }}</p>
                            @else
                                <div class="avatar size-20">
                                    <img class="rounded-full" src="{{ asset('images/200x200.png') }}"
                                        alt="avatar" />
                                </div>
                                <h3 class="mt-2 text-lg font-medium text-slate-700 dark:text-navy-100">No conversation
                                    selected</h3>
                                <p class="text-sm text-slate-500 dark:text-navy-300">Select a conversation to view
                                    details</p>
                            @endif
                            <div class="mt-2 flex space-x-1.5">
                                <button
                                    class="btn size-10 rounded-full p-0 text-slate-600 hover:bg-slate-300/20 hover:text-primary focus:bg-slate-300/20 focus:text-primary active:bg-slate-300/25 dark:text-navy-100 dark:hover:bg-navy-300/20 dark:hover:text-accent dark:focus:bg-navy-300/20 dark:focus:text-accent dark:active:bg-navy-300/25">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="size-6" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                    </svg>
                                </button>
                                <button
                                    class="btn size-10 rounded-full p-0 text-slate-600 hover:bg-slate-300/20 hover:text-primary focus:bg-slate-300/20 focus:text-primary active:bg-slate-300/25 dark:text-navy-100 dark:hover:bg-navy-300/20 dark:hover:text-accent dark:focus:bg-navy-300/20 dark:focus:text-accent dark:active:bg-navy-300/25">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="size-6" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                    </svg>
                                </button>
                                <button
                                    class="btn size-10 rounded-full p-0 text-slate-600 hover:bg-slate-300/20 hover:text-primary focus:bg-slate-300/20 focus:text-primary active:bg-slate-300/25 dark:text-navy-100 dark:hover:bg-navy-300/20 dark:hover:text-accent dark:focus:bg-navy-300/20 dark:focus:text-accent dark:active:bg-navy-300/25">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="size-6" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div x-data="{ activeTab: 'attachments' }" class="tabs mt-6 flex flex-col">
                            <div class="is-scrollbar-hidden overflow-x-auto px-4">
                                <div class="tabs-list flex">
                                    <button @click="activeTab = 'attachments'"
                                        :class="activeTab === 'attachments' ?
                                            'border-primary dark:border-accent text-primary dark:text-accent-light' :
                                            'border-transparent hover:text-slate-800 focus:text-slate-800 dark:hover:text-navy-100 dark:focus:text-navy-100'"
                                        class="btn shrink-0 rounded-none border-b-2 px-3 py-2 font-medium">
                                        Attachments
                                    </button>
                                    <button @click="activeTab = 'info'"
                                        :class="activeTab === 'info' ?
                                            'border-primary dark:border-accent text-primary dark:text-accent-light' :
                                            'border-transparent hover:text-slate-800 focus:text-slate-800 dark:hover:text-navy-100 dark:focus:text-navy-100'"
                                        class="btn shrink-0 rounded-none border-b-2 px-3 py-2 font-medium">
                                        Contact Info
                                    </button>
                                    <button @click="activeTab = 'reservations'"
                                        :class="activeTab === 'reservations' ?
                                            'border-primary dark:border-accent text-primary dark:text-accent-light' :
                                            'border-transparent hover:text-slate-800 focus:text-slate-800 dark:hover:text-navy-100 dark:focus:text-navy-100'"
                                        class="btn shrink-0 rounded-none border-b-2 px-3 py-2 font-medium">
                                        Reservations
                                    </button>
                                </div>
                            </div>
                            <div class="tab-content px-4 pt-4">
                                <div x-show="activeTab === 'attachments'"
                                    x-transition:enter="transition-all duration-500 easy-in-out"
                                    x-transition:enter-start="opacity-0 [transform:translate3d(1rem,0,0)]"
                                    x-transition:enter-end="opacity-100 [transform:translate3d(0,0,0)]">
                                    @include('livewire.social-bots.whatsapp.chatter-attachments')

                                </div>
                                <div x-show="activeTab === 'info'"
                                    x-transition:enter="transition-all duration-500 easy-in-out"
                                    x-transition:enter-start="opacity-0 [transform:translate3d(1rem,0,0)]"
                                    x-transition:enter-end="opacity-100 [transform:translate3d(0,0,0)]">
                                    <div class="flex flex-col space-y-3.5">
                                        @include('livewire.social-bots.whatsapp.chatter-info')
                                    </div>
                                </div>
                                <div x-show="activeTab === 'reservations'"
                                    x-transition:enter="transition-all duration-500 easy-in-out"
                                    x-transition:enter-start="opacity-0 [transform:translate3d(1rem,0,0)]"
                                    x-transition:enter-end="opacity-100 [transform:translate3d(0,0,0)]">
                                    <div class="flex flex-col space-y-3.5">
                                        @include('livewire.social-bots.whatsapp.chatter-reservations')
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </main>
    @script
        <script>
            // WhatsApp Messaging Alpine.js Component
            window.whatsappMessaging = function() {
                return {
                    // Local properties
                    message: '',
                    isSending: false,
                    isQueueEmpty: true,
                    queueStatus: null,
                    messageQueue: [],
                    currentUserId: null,
                    messageCallbacks: new Map(),
                    reverbChannel: null,
                    isListening: false,

                    // Initialize component
                    init() {
                        console.log('🚀 WhatsApp messaging component initializing...');
               
                    },

                    // Initialize messaging system
                    initializeMessaging() {
                        console.log('🚀 Initializing messaging system...');
                        WhatsAppMessaging.initialize();
                    },

                    // Setup cleanup when component is disposed
                    setupCleanup() {
                        window.addEventListener('beforeunload', () => {
                            console.log('👋 Cleaning up messaging system...');
                            WhatsAppMessaging.cleanup();
                        });
                    },

                    // Send message
                    sendMessage() {
                        if (!this.message.trim()) return;

                        this.isSending = true;
                        const messageData = {
                            recipient: 'recipient_id', // TODO: Get from UI
                            message: this.message.trim()
                        };

                        WhatsAppMessaging.sendMessage(messageData, {
                            onAck: (ack) => {
                                console.log('✅ Message ACK received:', ack);
                                this.isSending = false;
                            }
                        });

                        this.message = '';
                    }
                };
            };
        </script>
    @endscript
</div>
