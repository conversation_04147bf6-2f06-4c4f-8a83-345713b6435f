<?php

namespace App\Livewire\SocialBots;

use Livewire\Component;
use App\Models\WhatsApp\WhatsAppProfile;
use App\Models\WhatsApp\WhatsAppMessage;
use App\Models\WhatsApp\WhatsAppConnectedUser;
use App\Events\WhatsAppMessaging;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class Whatsapp extends Component
{
    // Chat data
    public $conversations = [];
    public $selectedConversation = null;
    public $messages = [];
    public $newMessage = '';
    public $sendingMessage = false;

    protected $listeners = [
        'loadConversationMessages' => 'loadConversationMessages',
        'refreshConversations' => 'loadConversations',
        'refreshMessages' => 'refreshMessages',
        'refreshMessageStatuses' => 'refreshMessageStatuses'
    ];

    public function mount()
    {
        $this->loadConversations();
    }

    public function loadConversations()
    {

        try {
            // Get the connected user for the authenticated user
            $connectedUser = WhatsAppConnectedUser::where('user_id', Auth::id())->first();

            if (!$connectedUser) {
                $this->conversations = [];
                return;
            }

            // Get profiles with their last message and unread count for the connected user
            $this->conversations = WhatsAppProfile::select([
                'profiles.id',
                'profiles.whatsapp_id',
                'profiles.name',
                'profiles.phone_number',
                'profiles.profile_picture',
                'last_messages.content as last_message_content',
                'last_messages.type as last_message_type',
                'last_messages.sent_at as last_message_time',
                'last_messages.is_outgoing as last_message_is_outgoing',
                DB::raw('COUNT(CASE WHEN messages.is_read = 0 AND messages.is_outgoing = 0 THEN 1 END) as unread_count')
            ])
                ->where('profiles.connected_user_id', $connectedUser->id)
                ->leftJoin('messages', 'profiles.id', '=', 'messages.profile_id')
                ->leftJoin('messages as last_messages', function ($join) {
                    $join->on('profiles.id', '=', 'last_messages.profile_id')
                        ->whereRaw('last_messages.id = (
                         SELECT MAX(id) FROM messages
                         WHERE messages.profile_id = profiles.id
                     )');
                })
                ->groupBy([
                    'profiles.id',
                    'profiles.whatsapp_id',
                    'profiles.name',
                    'profiles.phone_number',
                    'profiles.profile_picture',
                    'last_messages.content',
                    'last_messages.type',
                    'last_messages.sent_at',
                    'last_messages.is_outgoing'
                ])
                ->orderBy('last_messages.sent_at', 'desc')
                ->get()
                ->map(function ($conversation) {
                    return [
                        'id' => $conversation->id,
                        'whatsapp_id' => $conversation->whatsapp_id,
                        'name' => $conversation->name ?: $conversation->phone_number,
                        'phone_number' => $conversation->phone_number,
                        'profile_picture' => $conversation->profile_picture ?: asset('images/200x200.png'),
                        'last_message_content' => $conversation->last_message_content,
                        'last_message_type' => $conversation->last_message_type,
                        'last_message_time' => $conversation->last_message_time ? \Carbon\Carbon::parse($conversation->last_message_time)->format('H:i') : '',
                        'last_message_is_outgoing' => $conversation->last_message_is_outgoing,
                        'unread_count' => $conversation->unread_count
                    ];
                })
                ->toArray();
        } catch (\Exception $e) {
            $this->conversations = [];
            session()->flash('error', 'Failed to load conversations: ' . $e->getMessage());
        }
    }
   
    public function loadConversationMessages($profileId)
    {
        $this->selectedConversation = null;
        $this->messages = [];

        try {
            // Get the connected user for the authenticated user
            $connectedUser = WhatsAppConnectedUser::where('user_id', Auth::id())->first();

            if (!$connectedUser) {
                session()->flash('error', 'No WhatsApp connection found');
                return;
            }

            // Get the profile (ensure it belongs to the connected user)
            $profile = WhatsAppProfile::where('id', $profileId)
                ->where('connected_user_id', $connectedUser->id)
                ->first();

            if (!$profile) {
                session()->flash('error', 'Profile not found or access denied');
                return;
            }

            $this->selectedConversation = [
                'id' => $profile->id,
                'name' => $profile->name ?: $profile->phone_number,
                'phone_number' => $profile->phone_number,
                'profile_picture' => $profile->profile_picture ?: asset('images/200x200.png'),
                'is_business' => $profile->is_business,
                'is_group' => $profile->is_group,
                'whatsapp_id' => $profile->whatsapp_id
            ];

            // Get messages for this profile with attachments
            $this->messages = WhatsAppMessage::where('profile_id', $profileId)
                ->with('attachments')
                ->orderBy('sent_at', 'asc')
                ->get()
                ->map(function ($message) {
                    return [
                        'id' => $message->id,
                        'message_id' => $message->message_id,
                        'type' => $message->type,
                        'content' => $message->content,
                        'attachments' => $message->attachments->map(function ($attachment) {
                            return [
                                'id' => $attachment->id,
                                'filename' => $attachment->filename,
                                'mime_type' => $attachment->mime_type,
                                'file_size' => $attachment->file_size,
                                'file_url' => $attachment->file_url,
                                'whatsapp_media_url' => $attachment->file_url, // For backward compatibility
                                'is_image' => $attachment->isImage(),
                                'is_video' => $attachment->isVideo(),
                                'is_audio' => $attachment->isAudio(),
                                'is_document' => $attachment->isDocument(),
                            ];
                        })->toArray(),
                        'has_attachments' => $message->attachments->count() > 0,
                        'is_outgoing' => $message->is_outgoing,
                        'is_read' => $message->is_read,
                        'sent_at' => $message->sent_at,
                        'formatted_time' => $message->sent_at->format('H:i'),
                        'formatted_date' => $message->sent_at->format('Y-m-d')
                    ];
                })
                ->toArray();

            // Mark messages as read
            WhatsAppMessage::where('profile_id', $profileId)
                ->where('is_outgoing', false)
                ->where('is_read', false)
                ->update(['is_read' => true]);

            // Refresh conversations to update unread count
            $this->loadConversations();
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to load messages: ' . $e->getMessage());
        }
    }

    public function sendMessage()
    {
        if (!$this->selectedConversation || empty(trim($this->newMessage))) {
            return;
        }

        $this->sendingMessage = true;

        try {
            // Create the message in the database
            $message = WhatsAppMessage::create([
                'message_id' => 'msg_' . time() . '_' . rand(1000, 9999),
                'profile_id' => $this->selectedConversation['id'],
                'type' => 'text',
                'content' => trim($this->newMessage),
                'is_outgoing' => true,
                'is_read' => true, // Outgoing messages are automatically read
                'sent_at' => now(),
            ]);

            // Add to current messages array
            $this->messages[] = [
                'id' => $message->id,
                'message_id' => $message->message_id,
                'type' => $message->type,
                'content' => $message->content,
                'attachments' => [], // No attachments for text messages
                'is_outgoing' => $message->is_outgoing,
                'is_read' => $message->is_read,
                'sent_at' => $message->sent_at,
                'formatted_time' => $message->sent_at->format('H:i'),
                'formatted_date' => $message->sent_at->format('Y-m-d')
            ];

            // Clear the input
            $this->newMessage = '';

            // Refresh conversations to update last message
            $this->loadConversations();

            // Fire the messaging event to update UI globally
            broadcast(new WhatsAppMessaging(
                Auth::id(),
                $this->selectedConversation['id'],
                'message_received' // This will trigger UI refresh
            ));

            session()->flash('success', 'Message sent successfully');
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to send message: ' . $e->getMessage());
        }

        $this->sendingMessage = false;
    }

    /**
     * Refresh messages for a specific profile (called by messaging events)
     */
    public function refreshMessages($profileId)
    {
        // Get the connected user for the authenticated user
        $connectedUser = WhatsAppConnectedUser::where('user_id', Auth::id())->first();

        if (!$connectedUser) {
            return; // No connected user found
        }

        // Verify the profile belongs to the connected user
        $profile = WhatsAppProfile::where('id', $profileId)
            ->where('connected_user_id', $connectedUser->id)
            ->first();

        if (!$profile) {
            return; // Profile doesn't belong to this connected user
        }

        // If this profile is currently selected, reload its messages
        if ($this->selectedConversation && $this->selectedConversation['id'] == $profileId) {
            $this->loadConversationMessages($profileId);
        }

        // Always refresh conversations to update last message and unread counts
        $this->loadConversations();
    }

    /**
     * Refresh message statuses for a specific profile (called by ACK events)
     */
    public function refreshMessageStatuses($profileId)
    {
        // Get the connected user for the authenticated user
        $connectedUser = WhatsAppConnectedUser::where('user_id', Auth::id())->first();

        if (!$connectedUser) {
            return; // No connected user found
        }

        // Verify the profile belongs to the connected user
        $profile = WhatsAppProfile::where('id', $profileId)
            ->where('connected_user_id', $connectedUser->id)
            ->first();

        if (!$profile) {
            return; // Profile doesn't belong to this connected user
        }

        // If this profile is currently selected, reload its messages to get updated statuses
        if ($this->selectedConversation && $this->selectedConversation['id'] == $profileId) {
            $this->loadConversationMessages($profileId);
        }

        // Refresh conversations to update any status indicators
        $this->loadConversations();
    }

    /**
     * Get all attachments from the selected conversation
     */
    public function getConversationAttachmentsProperty()
    {
        if (!$this->selectedConversation || empty($this->messages)) {
            return collect();
        }

        $attachments = collect();

        foreach ($this->messages as $message) {
            if (!empty($message['attachments'])) {
                foreach ($message['attachments'] as $attachment) {
                    $attachments->push($attachment);
                }
            }
        }

        return $attachments;
    }

    public function render()
    {
        return view('livewire.social-bots.whatsapp.whatsapp-main')->layout(
            'components.base-layout',
            [
                'isSidebarOpen' => 'true',
                'title' => 'Whatsapp Bot',
                'hasMinSidebar' => 'true'
            ]
        );
    }
}
